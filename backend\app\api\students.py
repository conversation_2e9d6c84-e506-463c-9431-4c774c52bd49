from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File
from sqlmodel import Session, select
from typing import List, Optional
from uuid import UUID
import logging
from datetime import datetime, timezone

from core.database import get_session
from models.student import Student, StudentCreate, StudentRead, StudentUpdate, StudentStatus
from models.fee import StudentFee
from schemas.fee import FeeRead
from core.security import get_current_user
from models.user import AdminRole

logger = logging.getLogger(__name__)
router = APIRouter(tags=["Students"])

# =============== #
# Auth dependency #
# =============== #

def admin_or_staff(user=Depends(get_current_user)):
    if user.role not in [AdminRole.ADMIN.value, AdminRole.SUPER_ADMIN.value, AdminRole.STAFF.value]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You do not have permission to perform this action.",
        )
    return user

# ============== #
# List students #
# ============== #

@router.get("/", response_model=dict)
def read_students(
    search: Optional[str] = Query(None, description="Search by name, email, or admission number"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(10, ge=1, le=100, description="Page size"),
    class_id: Optional[int] = Query(None, description="Filter by class ID"),
    section_id: Optional[int] = Query(None, description="Filter by section ID"),
    status: Optional[str] = Query(None, description="Filter by status"),
    session: Session = Depends(get_session),
    user=Depends(admin_or_staff)
):
    """Get paginated list of students with filtering and search."""
    try:
        # Build query
        query = select(Student)
        
        # Apply filters
        if search:
            search_term = f"%{search}%"
            query = query.where(
                (Student.name.contains(search_term)) |
                (Student.surname.contains(search_term)) |
                (Student.email.contains(search_term)) |
                (Student.admission_number.contains(search_term))
            )
        
        if class_id:
            query = query.where(Student.class_id == class_id)
        
        if section_id:
            # Note: section_id might need to be joined with class table
            # For now, we'll use grade_id as section_id
            query = query.where(Student.grade_id == section_id)
        
        if status:
            try:
                student_status = StudentStatus(status)
                query = query.where(Student.status == student_status)
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid status value")
        
        # Get total count
        total_query = select(Student).where(query.whereclause) if query.whereclause else select(Student)
        total = len(session.exec(total_query).all())
        
        # Apply pagination
        offset = (page - 1) * size
        query = query.offset(offset).limit(size)
        
        # Execute query
        students = session.exec(query).all()
        
        # Convert to response format
        items = []
        for student in students:
            student_dict = {
                "id": str(student.id),
                "reg_no": student.admission_number,
                "first_name": student.name,
                "last_name": student.surname,
                "gender": student.sex,
                "dob": student.date_of_birth.isoformat() if student.date_of_birth else None,
                "class_id": student.class_id,
                "section_id": student.grade_id,
                "guardian_name": student.guardian_name,
                "guardian_phone": student.guardian_phone,
                "address": student.address,
                "email": student.email,
                "photo_url": student.img,
                "is_active": student.is_active,
                "created_at": student.created_at.isoformat() if student.created_at else None
            }
            items.append(student_dict)
        
        # Calculate pagination info
        total_pages = (total + size - 1) // size
        
        return {
            "items": items,
            "page": page,
            "size": size,
            "total": total,
            "pages": total_pages
        }
        
    except Exception as e:
        logger.error(f"Error fetching students: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch students"
        )

# ============== #
# Create student #
# ============== #

@router.post("/", response_model=dict, status_code=status.HTTP_201_CREATED)
def create_student(
    student_in: StudentCreate,
    session: Session = Depends(get_session),
    user=Depends(admin_or_staff)
):
    """Create a new student."""
    from core.security import get_password_hash
    from datetime import datetime, timezone
    import uuid

    try:
        # Check for duplicate admission number
        existing_student = session.exec(
            select(Student).where(Student.admission_number == student_in.admission_number)
        ).first()
        
        if existing_student:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Student with this admission number already exists"
            )
        
        # Check for duplicate email if provided
        if student_in.email:
            existing_email = session.exec(
                select(Student).where(Student.email == student_in.email)
            ).first()
            
            if existing_email:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail="Student with this email already exists"
                )
        
        # Hash the password
        hashed_password = get_password_hash(student_in.password)

        # Create student with all required fields
        student_data = student_in.dict(exclude={
            "password",
            "enrollment_date",
            "created_at",
            "updated_at"
        })

        # Add required fields
        student = Student(
            id=str(uuid.uuid4()),
            password=hashed_password,
            enrollment_date=datetime.now(timezone.utc),
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
            **student_data
        )

        session.add(student)
        session.commit()
        session.refresh(student)
        
        # Return in the expected format
        return {
            "id": str(student.id),
            "reg_no": student.admission_number,
            "first_name": student.name,
            "last_name": student.surname,
            "gender": student.sex,
            "dob": student.date_of_birth.isoformat() if student.date_of_birth else None,
            "class_id": student.class_id,
            "section_id": student.grade_id,
            "guardian_name": student.guardian_name,
            "guardian_phone": student.guardian_phone,
            "address": student.address,
            "email": student.email,
            "photo_url": student.img,
            "is_active": student.is_active,
            "created_at": student.created_at.isoformat() if student.created_at else None
        }

    except HTTPException:
        raise
    except Exception as e:
        session.rollback()
        logger.error(f"Failed to create student: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create student: {str(e)}"
        )

# ============== #
# Update student #
# ============== #

@router.put("/{student_id}", response_model=dict)
def update_student(
    student_id: str,
    student_in: StudentUpdate,
    session: Session = Depends(get_session),
    user=Depends(admin_or_staff)
):
    """Update an existing student."""
    from core.security import get_password_hash
    from datetime import datetime, timezone

    try:
        student = session.get(Student, student_id)
        if not student:
            raise HTTPException(status_code=404, detail="Student not found.")

        # Check for duplicate admission number if being updated
        if student_in.admission_number and student_in.admission_number != student.admission_number:
            existing_student = session.exec(
                select(Student).where(Student.admission_number == student_in.admission_number)
            ).first()
            
            if existing_student:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail="Student with this admission number already exists"
                )
        
        # Check for duplicate email if being updated
        if student_in.email and student_in.email != student.email:
            existing_email = session.exec(
                select(Student).where(Student.email == student_in.email)
            ).first()
            
            if existing_email:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail="Student with this email already exists"
                )

        # Get only the fields that were actually provided
        student_data = student_in.dict(exclude_unset=True, exclude_none=True)

        # Handle password hashing if password is being updated
        if "password" in student_data and student_data["password"]:
            if student_data["password"] not in ["string", "stringst"]:
                student_data["password"] = get_password_hash(student_data["password"])
            else:
                del student_data["password"]

        # Update timestamp
        student_data["updated_at"] = datetime.now(timezone.utc)

        # Update the student fields
        for key, value in student_data.items():
            setattr(student, key, value)

        session.add(student)
        session.commit()
        session.refresh(student)
        
        # Return in the expected format
        return {
            "id": str(student.id),
            "reg_no": student.admission_number,
            "first_name": student.name,
            "last_name": student.surname,
            "gender": student.sex,
            "dob": student.date_of_birth.isoformat() if student.date_of_birth else None,
            "class_id": student.class_id,
            "section_id": student.grade_id,
            "guardian_name": student.guardian_name,
            "guardian_phone": student.guardian_phone,
            "address": student.address,
            "email": student.email,
            "photo_url": student.img,
            "is_active": student.is_active,
            "created_at": student.created_at.isoformat() if student.created_at else None
        }

    except HTTPException:
        raise
    except Exception as e:
        session.rollback()
        logger.error(f"Failed to update student: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to update student: {str(e)}"
        )

# ============== #
# Toggle active  #
# ============== #

@router.post("/{student_id}/toggle-active", response_model=dict)
def toggle_student_active(
    student_id: str,
    session: Session = Depends(get_session),
    user=Depends(admin_or_staff)
):
    """Toggle student active status."""
    try:
        student = session.get(Student, student_id)
        if not student:
            raise HTTPException(status_code=404, detail="Student not found.")

        # Toggle the active status
        student.is_active = not student.is_active
        student.updated_at = datetime.now(timezone.utc)
        
        session.add(student)
        session.commit()
        session.refresh(student)
        
        return {
            "id": str(student.id),
            "is_active": student.is_active
        }

    except HTTPException:
        raise
    except Exception as e:
        session.rollback()
        logger.error(f"Failed to toggle student active status: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to toggle student active status: {str(e)}"
        )

# ============== #
# Delete student #
# ============== #

@router.delete("/{student_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_student(
    student_id: str,
    session: Session = Depends(get_session),
    user=Depends(admin_or_staff)
):
    """Soft delete a student by setting is_active to False."""
    try:
        student = session.get(Student, student_id)
        if not student:
            raise HTTPException(status_code=404, detail="Student not found.")

        # Soft delete by setting is_active to False
        student.is_active = False
        student.status = StudentStatus.INACTIVE
        student.updated_at = datetime.now(timezone.utc)
        
        session.add(student)
        session.commit()
        return None

    except HTTPException:
        raise
    except Exception as e:
        session.rollback()
        logger.error(f"Failed to delete student: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to delete student: {str(e)}"
        )

# ============== #
# Upload photo   #
# ============== #

@router.post("/{student_id}/photo", response_model=dict)
async def upload_student_photo(
    student_id: str,
    file: UploadFile = File(...),
    session: Session = Depends(get_session),
    user=Depends(admin_or_staff)
):
    """Upload student photo."""
    try:
        student = session.get(Student, student_id)
        if not student:
            raise HTTPException(status_code=404, detail="Student not found.")

        # Validate file type
        if not file.content_type.startswith('image/'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File must be an image"
            )

        # In a real implementation, you would:
        # 1. Save the file to a storage service (S3, local filesystem, etc.)
        # 2. Generate a URL for the uploaded file
        # 3. Update the student's photo_url field
        
        # For now, we'll simulate this with a placeholder URL
        photo_url = f"/uploads/students/{student_id}/{file.filename}"
        
        # Update student record
        student.img = photo_url
        student.updated_at = datetime.now(timezone.utc)
        
        session.add(student)
        session.commit()
        
        return {"photo_url": photo_url}

    except HTTPException:
        raise
    except Exception as e:
        session.rollback()
        logger.error(f"Failed to upload student photo: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to upload student photo: {str(e)}"
        )

# ============== #
# Import students #
# ============== #

@router.post("/import", response_model=dict)
async def import_students(
    file: UploadFile = File(...),
    session: Session = Depends(get_session),
    user=Depends(admin_or_staff)
):
    """Import students from CSV file."""
    try:
        # Validate file type
        if not file.filename.endswith('.csv'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File must be a CSV"
            )

        # In a real implementation, you would:
        # 1. Parse the CSV file
        # 2. Validate each row
        # 3. Create or update students
        # 4. Return results with created/updated/errors counts
        
        # For now, we'll return a placeholder response
        return {
            "created": 0,
            "updated": 0,
            "errors": []
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to import students: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to import students: {str(e)}"
        )

# ============== #
# Get student    #
# ============== #

@router.get("/{student_id}", response_model=dict)
def read_student(
    student_id: str,
    session: Session = Depends(get_session),
    user=Depends(admin_or_staff)
):
    """Get a specific student by ID."""
    try:
        student = session.get(Student, student_id)
        if not student:
            raise HTTPException(status_code=404, detail="Student not found.")

        # Return in the expected format
        return {
            "id": str(student.id),
            "reg_no": student.admission_number,
            "first_name": student.name,
            "last_name": student.surname,
            "gender": student.sex,
            "dob": student.date_of_birth.isoformat() if student.date_of_birth else None,
            "class_id": student.class_id,
            "section_id": student.grade_id,
            "guardian_name": student.guardian_name,
            "guardian_phone": student.guardian_phone,
            "address": student.address,
            "email": student.email,
            "photo_url": student.img,
            "is_active": student.is_active,
            "created_at": student.created_at.isoformat() if student.created_at else None
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get student: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get student: {str(e)}"
        )

# ======================= #
# Get student fees        #
# ======================= #

@router.get("/{student_id}/fees", response_model=List[FeeRead])
def get_student_fees(
    student_id: str,
    session: Session = Depends(get_session),
    user=Depends(admin_or_staff)
):
    """Get fees for a specific student."""
    try:
        fees = session.exec(
            select(StudentFee).where(
                StudentFee.student_id == UUID(student_id)
            )
        ).all()

        return fees

    except Exception as e:
        logger.error(f"Failed to get student fees: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get student fees: {str(e)}"
        )
